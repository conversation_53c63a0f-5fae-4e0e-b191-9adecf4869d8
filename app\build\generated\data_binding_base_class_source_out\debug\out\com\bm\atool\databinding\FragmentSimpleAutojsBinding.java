// Generated by view binder compiler. Do not edit!
package com.bm.atool.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bm.atool.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSimpleAutojsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnCheckStatus;

  @NonNull
  public final Button btnExecuteComplexScript;

  @NonNull
  public final Button btnExecuteSimpleScript;

  @NonNull
  public final Button btnExecuteTestScript;

  @NonNull
  public final Button btnStopAllScripts;

  @NonNull
  public final TextInputEditText etScriptContent;

  @NonNull
  public final TextInputEditText etScriptName;

  @NonNull
  public final TextView tvRunningScripts;

  @NonNull
  public final TextView tvScriptStatus;

  private FragmentSimpleAutojsBinding(@NonNull ScrollView rootView, @NonNull Button btnCheckStatus,
      @NonNull Button btnExecuteComplexScript, @NonNull Button btnExecuteSimpleScript,
      @NonNull Button btnExecuteTestScript, @NonNull Button btnStopAllScripts,
      @NonNull TextInputEditText etScriptContent, @NonNull TextInputEditText etScriptName,
      @NonNull TextView tvRunningScripts, @NonNull TextView tvScriptStatus) {
    this.rootView = rootView;
    this.btnCheckStatus = btnCheckStatus;
    this.btnExecuteComplexScript = btnExecuteComplexScript;
    this.btnExecuteSimpleScript = btnExecuteSimpleScript;
    this.btnExecuteTestScript = btnExecuteTestScript;
    this.btnStopAllScripts = btnStopAllScripts;
    this.etScriptContent = etScriptContent;
    this.etScriptName = etScriptName;
    this.tvRunningScripts = tvRunningScripts;
    this.tvScriptStatus = tvScriptStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSimpleAutojsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSimpleAutojsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_simple_autojs, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSimpleAutojsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCheckStatus;
      Button btnCheckStatus = ViewBindings.findChildViewById(rootView, id);
      if (btnCheckStatus == null) {
        break missingId;
      }

      id = R.id.btnExecuteComplexScript;
      Button btnExecuteComplexScript = ViewBindings.findChildViewById(rootView, id);
      if (btnExecuteComplexScript == null) {
        break missingId;
      }

      id = R.id.btnExecuteSimpleScript;
      Button btnExecuteSimpleScript = ViewBindings.findChildViewById(rootView, id);
      if (btnExecuteSimpleScript == null) {
        break missingId;
      }

      id = R.id.btnExecuteTestScript;
      Button btnExecuteTestScript = ViewBindings.findChildViewById(rootView, id);
      if (btnExecuteTestScript == null) {
        break missingId;
      }

      id = R.id.btnStopAllScripts;
      Button btnStopAllScripts = ViewBindings.findChildViewById(rootView, id);
      if (btnStopAllScripts == null) {
        break missingId;
      }

      id = R.id.etScriptContent;
      TextInputEditText etScriptContent = ViewBindings.findChildViewById(rootView, id);
      if (etScriptContent == null) {
        break missingId;
      }

      id = R.id.etScriptName;
      TextInputEditText etScriptName = ViewBindings.findChildViewById(rootView, id);
      if (etScriptName == null) {
        break missingId;
      }

      id = R.id.tvRunningScripts;
      TextView tvRunningScripts = ViewBindings.findChildViewById(rootView, id);
      if (tvRunningScripts == null) {
        break missingId;
      }

      id = R.id.tvScriptStatus;
      TextView tvScriptStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvScriptStatus == null) {
        break missingId;
      }

      return new FragmentSimpleAutojsBinding((ScrollView) rootView, btnCheckStatus,
          btnExecuteComplexScript, btnExecuteSimpleScript, btnExecuteTestScript, btnStopAllScripts,
          etScriptContent, etScriptName, tvRunningScripts, tvScriptStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
