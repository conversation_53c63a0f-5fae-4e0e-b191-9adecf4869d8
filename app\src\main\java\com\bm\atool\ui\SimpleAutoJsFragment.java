package com.bm.atool.ui;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.bm.atool.R;
import com.bm.atool.autojs.SimpleAutoJsManager;

/**
 * 简化的AutoJs脚本执行Fragment
 * 提供基本的JS脚本编辑和执行功能
 */
public class SimpleAutoJsFragment extends BaseFragment {
    private static final String TAG = "SimpleAutoJsFragment";
    
    private EditText etScriptContent;
    private EditText etScriptName;
    private Button btnExecuteSimpleScript;
    private Button btnExecuteComplexScript;
    private Button btnStopAllScripts;
    private Button btnCheckStatus;
    private TextView tvScriptStatus;
    private TextView tvRunningScripts;
    
    private SimpleAutoJsManager autoJsManager;
    private Handler uiHandler;
    
    public SimpleAutoJsFragment() {
        super();
        this.setTitle("JS脚本执行器");
    }
    
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView called");
        View view = inflater.inflate(R.layout.fragment_simple_autojs, container, false);

        initViews(view);
        initAutoJs();
        setupClickListeners();
        updateUI();

        Log.d(TAG, "Fragment initialization completed");

        // 移除自动执行，现在用户可以手动选择执行简单或复杂脚本

        return view;
    }
    
    private void initViews(View view) {
        etScriptContent = view.findViewById(R.id.etScriptContent);
        etScriptName = view.findViewById(R.id.etScriptName);
        btnExecuteSimpleScript = view.findViewById(R.id.btnExecuteSimpleScript);
        btnExecuteComplexScript = view.findViewById(R.id.btnExecuteComplexScript);
        btnStopAllScripts = view.findViewById(R.id.btnStopAllScripts);
        btnCheckStatus = view.findViewById(R.id.btnCheckStatus);
        tvScriptStatus = view.findViewById(R.id.tvScriptStatus);
        tvRunningScripts = view.findViewById(R.id.tvRunningScripts);

        uiHandler = new Handler();

        // 设置默认脚本内容
        setDefaultScript();
    }
    
    private void initAutoJs() {
        autoJsManager = SimpleAutoJsManager.getInstance();
        if (!autoJsManager.isInitialized()) {
            Log.e(TAG, "SimpleAutoJsManager not initialized");
            showToast("JS引擎未初始化，请重启应用");
        }
    }
    
    private void setupClickListeners() {
        btnExecuteSimpleScript.setOnClickListener(v -> executeSimpleScript());
        btnExecuteComplexScript.setOnClickListener(v -> executeComplexScript());
        btnStopAllScripts.setOnClickListener(v -> stopAllScripts());
        btnCheckStatus.setOnClickListener(v -> checkStatus());
    }
    
    private void setDefaultScript() {
        String defaultScript = "toast(\"Hello AutoJS!\");";

        etScriptContent.setText(defaultScript);
        etScriptName.setText("简单测试脚本");
    }
    
    private void executeSimpleScript() {
        Log.d(TAG, "executeSimpleScript button clicked");

        // 执行简单脚本：只显示toast
        String simpleScript = "toast(\"Hello AutoJS! 简单脚本执行成功！\");";
        String scriptName = "简单测试脚本";

        executeScriptInternal(simpleScript, scriptName);
    }

    private void executeComplexScript() {
        Log.d(TAG, "executeComplexScript button clicked");

        // 执行复杂脚本：测试多种功能
        String complexScript =
            "// 复杂脚本测试多种功能\n" +
            "console.log('开始执行复杂脚本测试...');\n" +
            "\n" +
            "// 1. 显示欢迎消息\n" +
            "toast('复杂脚本开始执行...');\n" +
            "\n" +
            "// 2. 延迟1秒\n" +
            "sleep(1000);\n" +
            "\n" +
            "// 3. 获取设备信息\n" +
            "console.log('设备宽度: ' + device.width);\n" +
            "console.log('设备高度: ' + device.height);\n" +
            "toast('设备尺寸: ' + device.width + 'x' + device.height);\n" +
            "\n" +
            "// 4. 再次延迟\n" +
            "sleep(1500);\n" +
            "\n" +
            "// 5. 获取当前包名\n" +
            "var packageName = currentPackage();\n" +
            "console.log('当前包名: ' + packageName);\n" +
            "toast('当前应用: ' + packageName);\n" +
            "\n" +
            "// 6. 延迟并显示完成消息\n" +
            "sleep(1000);\n" +
            "\n" +
            "// 7. 测试循环和计算\n" +
            "var sum = 0;\n" +
            "for (var i = 1; i <= 10; i++) {\n" +
            "    sum += i;\n" +
            "    console.log('计算中: ' + i + ', 当前和: ' + sum);\n" +
            "}\n" +
            "toast('1到10的和是: ' + sum);\n" +
            "\n" +
            "// 8. 最终完成消息\n" +
            "sleep(1000);\n" +
            "console.log('复杂脚本执行完成！');\n" +
            "toast('复杂脚本执行完成！所有功能测试通过！');";

        String scriptName = "复杂功能测试脚本";

        executeScriptInternal(complexScript, scriptName);
    }

    private void executeScriptInternal(String scriptContent, String scriptName) {
        Log.d(TAG, "executeScriptInternal called for: " + scriptName);
        Log.d(TAG, "Script name: " + scriptName + ", content length: " + scriptContent.length());

        if (TextUtils.isEmpty(scriptContent)) {
            showToast("脚本内容为空");
            return;
        }
        
        if (!autoJsManager.isInitialized()) {
            showToast("JS引擎未初始化");
            return;
        }
        
        try {
            // 执行脚本
            String scriptId = autoJsManager.executeScript(scriptContent, scriptName, 
                new SimpleAutoJsManager.ScriptExecutionCallback() {
                    @Override
                    public void onStart(String scriptName) {
                        uiHandler.post(() -> {
                            tvScriptStatus.setText("脚本正在执行: " + scriptName);
                            updateRunningScriptsCount();
                        });
                    }
                    
                    @Override
                    public void onSuccess(String scriptName, String result) {
                        uiHandler.post(() -> {
                            tvScriptStatus.setText("脚本执行成功: " + scriptName + " - " + result);
                            updateRunningScriptsCount();
                            showToast("脚本执行成功");
                        });
                    }
                    
                    @Override
                    public void onError(String scriptName, String error) {
                        uiHandler.post(() -> {
                            // 截取错误信息的前200个字符显示在UI上，避免文本过长
                            String errorPreview = error.length() > 200 ? error.substring(0, 200) + "..." : error;
                            tvScriptStatus.setText("脚本执行失败: " + scriptName + " - " + errorPreview);
                            updateRunningScriptsCount();
                            showToast("脚本执行失败: " + errorPreview);
                        });
                    }
                });
            
            if (scriptId != null) {
                Log.d(TAG, "Script execution started: " + scriptName + " (ID: " + scriptId + ")");
                showToast("脚本开始执行");
                updateRunningScriptsCount();
            } else {
                showToast("脚本执行失败");
                tvScriptStatus.setText("脚本执行失败");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error executing script", e);
            showToast("脚本执行出错: " + e.getMessage());
            tvScriptStatus.setText("脚本执行出错: " + e.getMessage());
        }
    }
    
    private void stopAllScripts() {
        try {
            autoJsManager.stopAllScripts();
            showToast("已停止所有脚本");
            tvScriptStatus.setText("已停止所有脚本");
            updateRunningScriptsCount();
        } catch (Exception e) {
            Log.e(TAG, "Error stopping scripts", e);
            showToast("停止脚本失败: " + e.getMessage());
        }
    }
    
    private void checkStatus() {
        if (!autoJsManager.isInitialized()) {
            showToast("JS引擎未初始化");
            return;
        }
        
        int runningCount = autoJsManager.getRunningScriptCount();
        String status = "JS引擎状态: 正常\n运行中的脚本: " + runningCount;
        
        showToast("JS引擎运行正常");
        tvScriptStatus.setText(status);
        updateRunningScriptsCount();
    }
    
    private void updateRunningScriptsCount() {
        try {
            int count = autoJsManager.getRunningScriptCount();
            tvRunningScripts.setText("运行中的脚本: " + count);
        } catch (Exception e) {
            Log.e(TAG, "Error getting running script count", e);
            tvRunningScripts.setText("运行中的脚本: 未知");
        }
    }
    
    private void updateUI() {
        updateRunningScriptsCount();
        
        if (autoJsManager.isInitialized()) {
            tvScriptStatus.setText("JS引擎已就绪");
        } else {
            tvScriptStatus.setText("JS引擎未初始化");
        }
    }
    
    private void showToast(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        updateUI();
    }
    
    @Override
    public int getIconResourceId() {
        return R.drawable.ic_code;
    }
}
