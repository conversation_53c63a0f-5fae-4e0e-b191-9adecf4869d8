<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_simple_autojs" modulePackage="com.bm.atool" filePath="app\src\main\res\layout\fragment_simple_autojs.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_simple_autojs_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="197" endOffset="12"/></Target><Target id="@+id/tvScriptStatus" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="42" endOffset="51"/></Target><Target id="@+id/tvRunningScripts" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="50" endOffset="58"/></Target><Target id="@+id/etScriptName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="63" startOffset="12" endLine="69" endOffset="68"/></Target><Target id="@+id/etScriptContent" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="82" startOffset="12" endLine="92" endOffset="68"/></Target><Target id="@+id/btnExecuteSimpleScript" view="Button"><Expressions/><location startLine="103" startOffset="12" endLine="113" endOffset="41"/></Target><Target id="@+id/btnExecuteComplexScript" view="Button"><Expressions/><location startLine="115" startOffset="12" endLine="125" endOffset="41"/></Target><Target id="@+id/btnExecuteTestScript" view="Button"><Expressions/><location startLine="127" startOffset="12" endLine="137" endOffset="41"/></Target><Target id="@+id/btnStopAllScripts" view="Button"><Expressions/><location startLine="144" startOffset="16" endLine="155" endOffset="45"/></Target><Target id="@+id/btnCheckStatus" view="Button"><Expressions/><location startLine="157" startOffset="16" endLine="167" endOffset="45"/></Target></Targets></Layout>