package com.bm.atool.autojs;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.webkit.ConsoleMessage;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import org.json.JSONObject;

import java.util.List;

import com.bm.atool.BuildConfig;


/**
 * 简化的JavaScript执行引擎
 * 使用WebView作为JS运行环境，提供基本的自动化功能
 */
public class SimpleJsEngine {
    private static final String TAG = "SimpleJsEngine";

    private Context context;
    private WebView webView;
    private Handler mainHandler;
    private JsExecutionCallback callback;
    private boolean webViewReady = false;

    public interface JsExecutionCallback {
        void onStart(String scriptName);
        void onSuccess(String scriptName, String result);
        void onError(String scriptName, String error);
    }

    public SimpleJsEngine(Context context) {
        this.context = context;
        this.mainHandler = new Handler(Looper.getMainLooper());
        Log.d(TAG, "SimpleJsEngine created, initializing WebView...");
        initWebView();
    }

    private void initWebView() {
        mainHandler.post(() -> {
            try {
                Log.d(TAG, "Creating WebView instance...");
                webView = new WebView(context);
                Log.d(TAG, "WebView instance created successfully");
                Log.d(TAG, "WebView settings: " + webView.getSettings().toString());
                WebSettings settings = webView.getSettings();
                settings.setJavaScriptEnabled(true);
                settings.setDomStorageEnabled(true);
                settings.setAllowFileAccess(true);
                settings.setAllowContentAccess(true);
                settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

                // 添加额外的安全和稳定性设置
                settings.setAllowFileAccessFromFileURLs(true);  // 允许从文件URL访问文件
                settings.setAllowUniversalAccessFromFileURLs(true);  // 允许从文件URL进行通用访问
                settings.setJavaScriptCanOpenWindowsAutomatically(false);
                settings.setMediaPlaybackRequiresUserGesture(false);

                // 设置缓存模式以提高稳定性
                settings.setCacheMode(WebSettings.LOAD_NO_CACHE);
                settings.setDatabaseEnabled(true);
                settings.setUseWideViewPort(true);
                settings.setLoadWithOverviewMode(true);
                
                // 启用调试模式（在debug版本中）
                if (BuildConfig.DEBUG) {
                    WebView.setWebContentsDebuggingEnabled(true);
                }

                // 添加JavaScript接口
                webView.addJavascriptInterface(new JsInterface(), "Android");
                Log.d(TAG, "JavaScript interface added successfully");

                // 设置WebViewClient来监听页面加载完成
                webView.setWebViewClient(new WebViewClient() {
                    @Override
                    public void onPageFinished(WebView view, String url) {
                        super.onPageFinished(view, url);
                        Log.d(TAG, "WebView page finished loading: " + url);
                        webViewReady = true;
                        Log.d(TAG, "WebView page loaded and ready");

                        // 直接注入API，不依赖HTML中的JavaScript
                        injectAPI();
                    }
                    
                    @Override
                    public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                        super.onReceivedError(view, errorCode, description, failingUrl);
                        Log.e(TAG, "WebView error: " + errorCode + " - " + description + " at " + failingUrl);
                        // 即使出现错误，我们也标记WebView为就绪状态，以便脚本可以执行
                        webViewReady = true;
                    }
                    
                    @Override
                    public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                        super.onReceivedError(view, request, error);
                        Log.e(TAG, "WebView error: " + error.getDescription() + " (Code: " + error.getErrorCode() + ")");
                        // 即使出现错误，我们也标记WebView为就绪状态，以便脚本可以执行
                        webViewReady = true;
                    }
                    
                    @Override
                    public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                        super.onPageStarted(view, url, favicon);
                        Log.d(TAG, "WebView page started loading: " + url);
                    }
                });

                // 设置WebChromeClient来捕获JavaScript控制台消息
                webView.setWebChromeClient(new WebChromeClient() {
                    @Override
                    public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                        String message = String.format("JS %s: %s (Source: %s, Line: %d)",
                                consoleMessage.messageLevel(),
                                consoleMessage.message(),
                                consoleMessage.sourceId(),
                                consoleMessage.lineNumber());
                        
                        // 使用字符串比较而不是枚举比较，以避免兼容性问题
                        String level = consoleMessage.messageLevel().name();
                        if ("ERROR".equals(level)) {
                            Log.e(TAG, "[CONSOLE ERROR] " + message);
                        } else if ("WARNING".equals(level)) {
                            Log.w(TAG, "[CONSOLE WARNING] " + message);
                        } else if ("INFO".equals(level)) {
                            Log.i(TAG, "[CONSOLE INFO] " + message);
                        } else if ("LOG".equals(level)) {
                            Log.d(TAG, "[CONSOLE LOG] " + message);
                        } else if ("DEBUG".equals(level)) {
                            Log.d(TAG, "[CONSOLE DEBUG] " + message);
                        } else {
                            Log.d(TAG, "[CONSOLE] " + message);
                        }
                        return true;
                    }
                    
                    @Override
                    public void onProgressChanged(WebView view, int newProgress) {
                        super.onProgressChanged(view, newProgress);
                        Log.d(TAG, "WebView loading progress: " + newProgress + "%");
                    }
                    
                    @Override
                    public void onReceivedTitle(WebView view, String title) {
                        super.onReceivedTitle(view, title);
                        Log.d(TAG, "WebView received title: " + title);
                    }
                });

                // 创建一个简单的HTML页面来承载JavaScript执行环境
                // API将通过injectAPI方法注入，不依赖HTML中的JavaScript
                String html = "<!DOCTYPE html>" +
                        "<html>" +
                        "<head>" +
                        "<meta charset='utf-8'>" +
                        "<title>SimpleAutoJs Engine</title>" +
                        "</head>" +
                        "<body>" +
                        "<script>" +
                        // 简单的初始化脚本，主要API将通过injectAPI方法注入
                        "console.log('[HTML] Page loaded, waiting for API injection...');" +
                        "</script>" +
                        "</body>" +
                        "</html>";

                // Validate HTML structure
                if (!html.contains("<script>") || !html.contains("</script>")) {
                    Log.e(TAG, "HTML validation failed: Missing script tags");
                }
                // API将通过injectAPI方法注入，不需要在HTML中验证autoJsReady
                
                Log.d(TAG, "Loading HTML into WebView, HTML length: " + html.length());
                // 只在DEBUG模式下记录完整的HTML内容，避免日志过大
                if (BuildConfig.DEBUG) {
                    Log.d(TAG, "HTML content: " + html);
                }
                // 使用loadData方式加载HTML，确保JavaScript能正确执行
                webView.loadData(html, "text/html; charset=utf-8", "utf-8");
                
                Log.d(TAG, "WebView initialized successfully");
            } catch (Exception e) {
                Log.e(TAG, "Error initializing WebView", e);
                // 即使初始化失败，我们也标记WebView为就绪状态，以便脚本可以执行
                webViewReady = true;
            }
        });
    }

    /**
     * 执行JavaScript脚本
     */
    public void executeScript(String scriptContent, String scriptName, JsExecutionCallback callback) {
        Log.d(TAG, "executeScript called: " + scriptName + ", content length: " + scriptContent.length());
        this.callback = callback;

        if (callback != null) {
            callback.onStart(scriptName);
        }

        // 等待WebView准备就绪后再执行脚本
        waitForWebViewReady(() -> executeScriptInternal(scriptContent, scriptName));
    }

    private void injectAPI() {
        if (webView == null) {
            Log.e(TAG, "Cannot inject API: WebView is null");
            return;
        }

        Log.d(TAG, "Injecting AutoJs API directly into WebView");

        // 直接注入API函数，不依赖HTML中的JavaScript
        String apiScript =
            "console.log('[API] Starting AutoJs API injection');" +

            // 重定向console输出到Android
            "window.originalConsole = { log: console.log, error: console.error, warn: console.warn, info: console.info };" +
            "console.log = function(msg) { try { Android.log('[JS] ' + String(msg)); } catch(e) { window.originalConsole.error('[API] Log error: ' + e); } };" +
            "console.warn = function(msg) { try { Android.log('[JS WARN] ' + String(msg)); } catch(e) { window.originalConsole.error('[API] Warn error: ' + e); } };" +
            "console.error = function(msg) { try { Android.log('[JS ERROR] ' + String(msg)); } catch(e) { window.originalConsole.error('[API] Error log error: ' + e); } };" +
            "console.info = function(msg) { try { Android.log('[JS INFO] ' + String(msg)); } catch(e) { window.originalConsole.error('[API] Info log error: ' + e); } };" +

            // 注入toast函数
            "window.toast = function(msg) { try { Android.toast(String(msg)); } catch(e) { console.error('[API] Toast error: ' + e); } };" +
            "var toast = window.toast;" +

            // 注入sleep函数
            "window.sleep = function(ms) { var start = Date.now(); while (Date.now() - start < ms) { } };" +
            "var sleep = window.sleep;" +

            // 注入device对象
            "window.device = { width: window.screen ? window.screen.width : 1080, height: window.screen ? window.screen.height : 1920, brand: 'Android', model: 'Unknown' };" +
            "var device = window.device;" +

            // 注入auto对象
            "window.auto = { service: false };" +
            "var auto = window.auto;" +

            // 注入currentPackage函数
            "window.currentPackage = function() { try { return Android.getCurrentPackage(); } catch(e) { console.error('[API] CurrentPackage error: ' + e); return 'unknown'; } };" +
            "var currentPackage = window.currentPackage;" +

            // 注入openUrl函数
            "window.openUrl = function(url) { try { Android.openUrl(String(url)); console.log('[API] Opening URL: ' + url); } catch(e) { console.error('[API] OpenUrl error: ' + e); } };" +
            "var openUrl = window.openUrl;" +

            // 注入launchApp函数
            "window.launchApp = function(packageName) { try { var result = Android.launchApp(String(packageName)); console.log('[API] Launching app: ' + packageName + ', result: ' + result); return result; } catch(e) { console.error('[API] LaunchApp error: ' + e); return false; } };" +
            "var launchApp = window.launchApp;" +

            // 注入isAppInstalled函数
            "window.isAppInstalled = function(packageName) { try { var result = Android.isAppInstalled(String(packageName)); console.log('[API] Checking if app installed: ' + packageName + ', result: ' + result); return result; } catch(e) { console.error('[API] IsAppInstalled error: ' + e); return false; } };" +
            "var isAppInstalled = window.isAppInstalled;" +

            // 设置API就绪标志
            "window.autoJsReady = true;" +
            "console.log('[API] AutoJs API injected successfully');" +
            "console.log('[API] Available functions: toast=' + (typeof window.toast) + ', sleep=' + (typeof window.sleep) + ', device=' + (typeof window.device) + ', currentPackage=' + (typeof window.currentPackage) + ', openUrl=' + (typeof window.openUrl) + ', launchApp=' + (typeof window.launchApp) + ', isAppInstalled=' + (typeof window.isAppInstalled));";

        webView.evaluateJavascript(apiScript, result -> {
            Log.d(TAG, "API injection completed, result: " + result);
        });
    }

    private void waitForWebViewReady(Runnable onReady) {
        waitForWebViewReady(onReady, 0);
    }
    
    private void waitForWebViewReady(Runnable onReady, int retryCount) {
        mainHandler.post(() -> {
            if (webView == null) {
                Log.e(TAG, "WebView is null when waiting for ready state");
                if (callback != null) {
                    callback.onError("Script", "WebView not initialized");
                }
                return;
            }
            
            if (webViewReady) {
                Log.d(TAG, "WebView is ready, executing script");
                onReady.run();
            } else {
                // 增加重试次数限制，避免无限等待
                if (retryCount > 100) { // 最多等待10秒 (100 * 100ms)
                    Log.e(TAG, "WebView not ready after maximum retries (100 attempts, 10 seconds timeout)");
                    if (callback != null) {
                        callback.onError("Script", "WebView not ready after maximum retries (10 seconds timeout)");
                    }
                    return;
                }
                
                Log.d(TAG, "WebView not ready yet, waiting... (retry: " + retryCount + ")");
                // 如果WebView还没准备好，等待一段时间后重试
                mainHandler.postDelayed(() -> waitForWebViewReady(onReady, retryCount + 1), 100);
            }
        });
    }

    private void executeScriptInternal(String scriptContent, String scriptName) {
        Log.d(TAG, "executeScriptInternal called for: " + scriptName);
        mainHandler.post(() -> {
            try {
                Log.d(TAG, "About to execute script in WebView");
                Log.d(TAG, "Script content length: " + scriptContent.length());
                
                // 在DEBUG模式下记录脚本内容的前200个字符，避免日志过大
                if (BuildConfig.DEBUG) {
                    String preview = scriptContent.length() > 200 ? scriptContent.substring(0, 200) + "..." : scriptContent;
                    Log.d(TAG, "Script content preview: " + preview);
                }

                // 包装脚本以捕获错误和结果，确保API已加载
                // 使用 JSONObject.quote 安全地转义用户脚本，避免字符串拼接导致的语法错误
                final String scriptJson = JSONObject.quote(scriptContent);
                final String scriptNameJson = JSONObject.quote(scriptName);
                Log.d(TAG, "Script JSON prepared, length: " + scriptJson.length());

                // 修复脚本包装，确保API准备就绪后执行脚本
                String wrappedScript =
                    "setTimeout(function() {" +
                    "  try {" +
                    "    console.log('[JS] Starting script execution');" +
                    "    console.log('[JS] API ready state: ' + window.autoJsReady);" +
                    "    console.log('[JS] Available functions: toast=' + (typeof window.toast) + ', sleep=' + (typeof window.sleep) + ', device=' + (typeof window.device) + ', currentPackage=' + (typeof window.currentPackage) + ', openUrl=' + (typeof window.openUrl) + ', launchApp=' + (typeof window.launchApp) + ', isAppInstalled=' + (typeof window.isAppInstalled));" +
                    "    if (typeof window.autoJsReady === 'undefined' || window.autoJsReady !== true) {" +
                    "      console.log('[JS] API not ready, waiting for initialization...');" +
                    "      setTimeout(arguments.callee, 100);" +
                    "      return;" +
                    "    }" +
                    "    console.log('[JS] API ready, executing script');" +
                    "    eval(" + scriptJson + ");" +
                    "    console.log('[JS] Script completed successfully');" +
                    "    Android.onScriptSuccess(" + scriptNameJson + ", 'completed');" +
                    "  } catch (e) {" +
                    "    console.error('[JS] Script execution error: ' + e.message);" +
                    "    if (e.stack) console.error('[JS] Stack trace: ' + e.stack);" +
                    "    Android.onScriptError(" + scriptNameJson + ", 'Script error: ' + e.message + (e.stack ? '\\\\nStack: ' + e.stack : ''));" +
                    "  }" +
                    "}, 200);";

                Log.d(TAG, "About to call webView.evaluateJavascript");
                // 添加执行前的日志记录
                Log.d(TAG, "Executing script with name: " + scriptName);
                Log.d(TAG, "Wrapped script length: " + wrappedScript.length());
                
                // 在DEBUG模式下记录包装脚本的前500个字符
                if (BuildConfig.DEBUG) {
                    String wrappedPreview = wrappedScript.length() > 500 ? wrappedScript.substring(0, 500) + "..." : wrappedScript;
                    Log.d(TAG, "Wrapped script preview: " + wrappedPreview);
                }
                
                webView.evaluateJavascript(wrappedScript, result -> {
                    // 添加执行后的日志记录
                    Log.d(TAG, "webView.evaluateJavascript completed for: " + scriptName);
                    if (result != null) {
                        Log.d(TAG, "JavaScript evaluation result: " + result);
                    } else {
                        Log.d(TAG, "JavaScript evaluation result is null");
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "Error executing script: " + scriptName, e);
                if (callback != null) {
                    callback.onError(scriptName, e.getMessage() + "\nStack: " + Log.getStackTraceString(e));
                }
            }
        });
    }

    /**
     * 停止脚本执行
     */
    public void stopScript() {
        mainHandler.post(() -> {
            try {
                if (webView != null) {
                    webView.evaluateJavascript("window.stop();", null);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error stopping script", e);
            }
        });
    }

    /**
     * 释放资源
     */
    public void release() {
        mainHandler.post(() -> {
            try {
                if (webView != null) {
                    webView.destroy();
                    webView = null;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error releasing WebView", e);
            }
        });
    }

    /**
     * JavaScript接口类
     */
    private class JsInterface {
        
        @JavascriptInterface
        public void log(String message) {
            try {
                Log.d(TAG, "JS Log: " + message);
            } catch (Exception e) {
                Log.e(TAG, "Error in log interface", e);
            }
        }
        
        @JavascriptInterface
        public void toast(String message) {
            Log.d(TAG, "Toast called with message: " + message);
            mainHandler.post(() -> {
                try {
                    if (context != null) {
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
                        Log.d(TAG, "Toast displayed: " + message);
                    } else {
                        Log.e(TAG, "Context is null, cannot show toast");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error showing toast", e);
                }
            });
        }
        
        @JavascriptInterface
        public String getCurrentPackage() {
            try {
                // 这里可以返回当前应用的包名
                if (context != null) {
                    return context.getPackageName();
                }
                return "unknown";
            } catch (Exception e) {
                Log.e(TAG, "Error getting current package", e);
                return "unknown";
            }
        }

        @JavascriptInterface
        public void openUrl(String url) {
            Log.d(TAG, "openUrl called with URL: " + url);
            mainHandler.post(() -> {
                try {
                    if (context != null) {
                        // 确保URL有协议前缀
                        String finalUrl = url;
                        if (!url.startsWith("http://") && !url.startsWith("https://")) {
                            finalUrl = "http://" + url;
                        }

                        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(finalUrl));
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        context.startActivity(intent);
                        Log.d(TAG, "URL opened successfully: " + finalUrl);
                    } else {
                        Log.e(TAG, "Context is null, cannot open URL");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error opening URL: " + url, e);
                }
            });
        }

        @JavascriptInterface
        public boolean launchApp(String packageName) {
            Log.d(TAG, "launchApp called with package: " + packageName);
            try {
                if (context != null) {
                    PackageManager packageManager = context.getPackageManager();

                    // 首先检查应用是否已安装
                    try {
                        packageManager.getPackageInfo(packageName, 0);
                        Log.d(TAG, "Package found: " + packageName);
                    } catch (Exception e) {
                        Log.e(TAG, "Package not found: " + packageName);
                        return false;
                    }

                    // 尝试获取启动Intent
                    Intent intent = packageManager.getLaunchIntentForPackage(packageName);
                    if (intent != null) {
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                        context.startActivity(intent);
                        Log.d(TAG, "App launched successfully: " + packageName);
                        return true;
                    } else {
                        Log.e(TAG, "Launch intent is null for package: " + packageName);

                        // 尝试备用方法：直接创建Intent
                        try {
                            Intent backupIntent = new Intent(Intent.ACTION_MAIN);
                            backupIntent.addCategory(Intent.CATEGORY_LAUNCHER);
                            backupIntent.setPackage(packageName);
                            backupIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            backupIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                            context.startActivity(backupIntent);
                            Log.d(TAG, "App launched successfully using backup method: " + packageName);
                            return true;
                        } catch (Exception backupException) {
                            Log.e(TAG, "Backup launch method also failed: " + packageName, backupException);
                            return false;
                        }
                    }
                } else {
                    Log.e(TAG, "Context is null, cannot launch app");
                    return false;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error launching app: " + packageName, e);
                return false;
            }
        }

        @JavascriptInterface
        public boolean isAppInstalled(String packageName) {
            Log.d(TAG, "isAppInstalled called with package: " + packageName);
            try {
                if (context != null) {
                    PackageManager packageManager = context.getPackageManager();
                    try {
                        packageManager.getPackageInfo(packageName, 0);
                        Log.d(TAG, "App is installed: " + packageName);
                        return true;
                    } catch (Exception e) {
                        Log.d(TAG, "App is not installed: " + packageName);
                        return false;
                    }
                } else {
                    Log.e(TAG, "Context is null, cannot check app installation");
                    return false;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error checking app installation: " + packageName, e);
                return false;
            }
        }

        @JavascriptInterface
        public void onScriptSuccess(String scriptName, String result) {
            mainHandler.post(() -> {
                try {
                    Log.d(TAG, "onScriptSuccess called for: " + scriptName + ", result: " + result);
                    if (callback != null) {
                        callback.onSuccess(scriptName, result);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in onScriptSuccess callback", e);
                }
            });
        }
        
        @JavascriptInterface
        public void onScriptError(String scriptName, String error) {
            mainHandler.post(() -> {
                try {
                    Log.e(TAG, "Script error reported from JS: " + scriptName);
                    // 分行记录错误信息，使其更易读
                    String[] errorLines = error.split("\n");
                    for (int i = 0; i < errorLines.length; i++) {
                        if (i == 0) {
                            Log.e(TAG, "Error message: " + errorLines[i]);
                        } else {
                            Log.e(TAG, "Error stack [" + i + "]: " + errorLines[i]);
                        }
                    }
                    if (callback != null) {
                        callback.onError(scriptName, error);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in onScriptError callback", e);
                }
            });
        }
    }
}